import React, { useState, useEffect } from 'react';
import { X, Edit, Trash2, Save } from 'lucide-react';

interface NotesModalProps {
  isOpen: boolean;
  onClose: () => void;
  leadId: string;
  leadName: string;
  existingNote?: string;
  onSave: (leadId: string, note: string) => Promise<void>;
  onDelete: (leadId: string) => Promise<void>;
}

const NotesModal: React.FC<NotesModalProps> = ({
  isOpen,
  onClose,
  leadId,
  leadName,
  existingNote = '',
  onSave,
  onDelete,
}) => {
  const [note, setNote] = useState(existingNote);
  const [isEditing, setIsEditing] = useState(!existingNote);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setNote(existingNote);
      setIsEditing(!existingNote);
    }
  }, [isOpen, existingNote]);

  const handleSave = async () => {
    if (!note.trim()) return;
    
    setIsSaving(true);
    try {
      await onSave(leadId, note.trim());
      setIsEditing(false);
      onClose();
    } catch (error) {
      console.error('Error saving note:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this note?')) return;
    
    setIsDeleting(true);
    try {
      await onDelete(leadId);
      onClose();
    } catch (error) {
      console.error('Error deleting note:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancel = () => {
    setNote(existingNote);
    setIsEditing(!existingNote);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            {existingNote ? 'Manage Note' : 'Add Note'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Note for: <span className="font-semibold">{leadName}</span>
            </label>
            <textarea
              value={note}
              onChange={(e) => setNote(e.target.value)}
              disabled={!isEditing}
              placeholder="Enter your note here..."
              className={`w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none ${
                !isEditing ? 'bg-gray-50 text-gray-600' : ''
              }`}
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-4 border-t border-gray-200">
          <div className="flex gap-2">
            {existingNote && !isEditing && (
              <button
                onClick={() => setIsEditing(true)}
                className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors"
              >
                <Edit className="w-4 h-4" />
                Edit
              </button>
            )}
            {existingNote && (
              <button
                onClick={handleDelete}
                disabled={isDeleting}
                className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-red-600 bg-red-50 rounded-md hover:bg-red-100 transition-colors disabled:opacity-50"
              >
                <Trash2 className="w-4 h-4" />
                {isDeleting ? 'Deleting...' : 'Delete'}
              </button>
            )}
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={handleCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            {isEditing && (
              <button
                onClick={handleSave}
                disabled={isSaving || !note.trim()}
                className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                <Save className="w-4 h-4" />
                {isSaving ? 'Saving...' : 'OK'}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotesModal;
